import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useToast } from '@/hooks/useToast';
import { getAuditById, markAuditFollowedUp } from '@/api/buyPawnAudits';
import { AuditNavigation } from '@/components/buys/audits/AuditNavigation';
import { format } from 'date-fns';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Loader2, ArrowLeft, Flag, CheckCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

/**
 * Component for viewing a single audit's details
 */
export function AuditDetails() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [isLoading, setIsLoading] = useState(true);
  const [audit, setAudit] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [followupResponse, setFollowupResponse] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Load audit details
  useEffect(() => {
    const loadAudit = async () => {
      if (!id) return;
      
      setIsLoading(true);
      try {
        const result = await getAuditById(id);
        
        if (result.success) {
          setAudit(result.data);
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to load audit details.',
            variant: 'destructive',
          });
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadAudit();
  }, [id, toast]);
  
  // Handle opening the follow-up dialog
  const handleOpenFollowup = () => {
    setFollowupResponse('');
    setDialogOpen(true);
  };
  
  // Handle submitting the follow-up
  const handleSubmitFollowup = async () => {
    if (!audit) return;
    
    if (!followupResponse.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please enter a follow-up response.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      const result = await markAuditFollowedUp(audit._id, followupResponse);
      
      if (result.success) {
        toast({
          title: 'Follow-up Recorded',
          description: 'The follow-up has been recorded successfully.',
        });
        
        // Update the audit
        setAudit({
          ...audit,
          followedUp: true,
          followupResponse,
          status: 'resolved',
        });
        
        // Close the dialog
        setDialogOpen(false);
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to record follow-up.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };
  
  // Get compliance badge
  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case 'compliant':
        return <Badge className="bg-green-500">Compliant</Badge>;
      case 'minor_non_compliant':
        return <Badge className="bg-yellow-500">Minor Non-Compliant</Badge>;
      case 'major_non_compliant':
        return <Badge className="bg-red-500">Major Non-Compliant</Badge>;
      default:
        return <Badge variant="outline">{compliance}</Badge>;
    }
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge className="bg-green-500">Pass</Badge>;
      case 'fail':
        return <Badge className="bg-red-500">Fail</Badge>;
      case 'not_assessed':
        return <Badge variant="outline">Not Assessed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }
  
  if (!audit) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Audit Not Found</CardTitle>
            <CardDescription>
              The audit you are looking for could not be found.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/buys/audits')}>Back to Dashboard</Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Audit Details</h1>
            <p className="text-muted-foreground">View detailed information about this audit</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => navigate('/buys/audits')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
            
            {audit.flaggedForFollowup && !audit.followedUp && (
              <Button onClick={handleOpenFollowup}>
                <Flag className="mr-2 h-4 w-4" />
                Record Follow-up
              </Button>
            )}
          </div>
        </div>
        
        <AuditNavigation />
      </div>
      
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Audit Summary</CardTitle>
            <CardDescription>Overview of the audit</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Transaction ID</p>
                <p className="font-medium">{audit.transactionId}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Audit Type</p>
                <p className="font-medium capitalize">{audit.auditType}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Employee</p>
                <p className="font-medium">{audit.employeeName}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Amount</p>
                <p className="font-medium">${parseFloat(audit.amount).toFixed(2)}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Audit Date</p>
                <p className="font-medium">{formatDate(audit.auditDate)}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Audited By</p>
                <p className="font-medium">{audit.auditedBy.fullName}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Compliance</p>
                <div>{getComplianceBadge(audit.overallCompliance)}</div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Score</p>
                <p className="font-medium">{audit.overallScore}%</p>
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Item Description</p>
              <p>{audit.itemDescription}</p>
            </div>
            
            {audit.auditNotes && (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Audit Notes</p>
                <p>{audit.auditNotes}</p>
              </div>
            )}
            
            {audit.flaggedForFollowup && (
              <>
                <Separator />
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">Follow-up Status</p>
                    {audit.followedUp ? (
                      <Badge variant="outline" className="bg-green-500/10 text-green-500">
                        <CheckCircle className="mr-1 h-3 w-3" /> Completed
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500">
                        <Flag className="mr-1 h-3 w-3" /> Pending
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Flag Reason</p>
                    <p>{audit.flagReason}</p>
                  </div>
                  
                  {audit.followedUp && (
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Follow-up Response</p>
                      <p>{audit.followupResponse}</p>
                    </div>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>
        
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Detailed audit section views are coming soon!
          </p>
        </div>
      </div>
      
      {/* Follow-up Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Record Follow-up</DialogTitle>
            <DialogDescription>
              Record the follow-up conversation with the employee.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium">Transaction ID</p>
              <p className="text-sm">{audit.transactionId}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium">Employee</p>
              <p className="text-sm">{audit.employeeName}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium">Flag Reason</p>
              <p className="text-sm">{audit.flagReason}</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="followupResponse">Follow-up Response</Label>
              <Textarea
                id="followupResponse"
                placeholder="Enter details of the follow-up conversation"
                value={followupResponse}
                onChange={(e) => setFollowupResponse(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmitFollowup} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Follow-up'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
