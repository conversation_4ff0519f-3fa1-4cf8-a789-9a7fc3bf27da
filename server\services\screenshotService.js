const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');
const schedule = require('node-schedule');
const sharp = require('sharp');
const os = require('os');
const { spawn } = require('child_process');
const DashboardSettings = require('../models/DashboardSettings');
const SCREENSHOTS_DIR = path.join(__dirname, '../public/screenshots');

// Ensure screenshots directory exists
if (!fs.existsSync(SCREENSHOTS_DIR)) {
  fs.mkdirSync(SCREENSHOTS_DIR, { recursive: true });
  console.log(`Created screenshots directory at: ${SCREENSHOTS_DIR}`);
}

// Define default cropping dimensions
const DEFAULT_CROP = {
  left: 53,   // Crop 53px from left
  right: 53,  // Crop 53px from right
  top: 0,     // Keep the top
  bottom: 60  // Crop 32px from bottom
};

// Virtual Display Manager
class VirtualDisplayManager {
  constructor() {
    this.platform = os.platform();
    this.xvfbProcess = null;
    this.displayNumber = null;
  }

  async setupVirtualDisplay() {
    updateStatus({
      message: `Setting up virtual display for platform: ${this.platform}`
    });

    if (this.platform === 'linux') {
      return await this.setupXvfb();
    } else if (this.platform === 'win32') {
      return await this.setupWindowsHeadless();
    } else {
      // For macOS and other platforms, use enhanced headless mode
      updateStatus({
        message: `Using enhanced headless mode for platform: ${this.platform}`
      });
      return this.getEnhancedHeadlessConfig();
    }
  }

  async setupXvfb() {
    try {
      // Find an available display number
      this.displayNumber = await this.findAvailableDisplay();

      updateStatus({
        message: `Starting Xvfb on display :${this.displayNumber}`
      });

      // Start Xvfb with appropriate settings
      const xvfbArgs = [
        `:${this.displayNumber}`,
        '-screen', '0', '1920x1080x24',
        '-ac',  // Disable access control
        '+extension', 'GLX',
        '+render',
        '-noreset'
      ];

      this.xvfbProcess = spawn('Xvfb', xvfbArgs, {
        stdio: 'pipe',
        detached: false
      });

      // Wait for Xvfb to start
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Xvfb startup timeout'));
        }, 10000);

        this.xvfbProcess.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });

        // Give Xvfb time to start
        setTimeout(() => {
          clearTimeout(timeout);
          resolve();
        }, 2000);
      });

      // Set DISPLAY environment variable
      process.env.DISPLAY = `:${this.displayNumber}`;

      updateStatus({
        message: `Xvfb started successfully on display :${this.displayNumber}`
      });

      return this.getLinuxPuppeteerConfig();

    } catch (error) {
      updateStatus({
        message: `Failed to setup Xvfb: ${error.message}. Falling back to headless mode.`
      });
      return this.getEnhancedHeadlessConfig();
    }
  }

  async setupWindowsHeadless() {
    updateStatus({
      message: 'Configuring enhanced headless mode for Windows'
    });
    return this.getWindowsPuppeteerConfig();
  }

  async findAvailableDisplay() {
    // Start from display 99 and work backwards to avoid conflicts
    for (let display = 99; display >= 10; display--) {
      try {
        // Check if display is in use by trying to connect
        const testProcess = spawn('xdpyinfo', [`-display`, `:${display}`], {
          stdio: 'pipe'
        });

        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            testProcess.kill();
            resolve(); // Display is available
          }, 1000);

          testProcess.on('exit', (code) => {
            clearTimeout(timeout);
            if (code === 0) {
              reject(); // Display is in use
            } else {
              resolve(); // Display is available
            }
          });
        });

        return display;
      } catch (error) {
        // Continue to next display number
        continue;
      }
    }

    // Default to display 99 if all else fails
    return 99;
  }

  getLinuxPuppeteerConfig() {
    return {
      headless: false, // Use the virtual display
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--window-size=1920,1080',
        '--start-maximized',
        '--no-first-run',
        '--disable-default-apps',
        '--disable-popup-blocking',
        '--disable-translate',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows'
      ]
    };
  }

  getWindowsPuppeteerConfig() {
    return {
      headless: 'new', // Use new headless mode for better compatibility
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-gpu-sandbox',
        '--disable-software-rasterizer',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI,VizDisplayCompositor',
        '--disable-ipc-flooding-protection',
        '--window-size=1920,1080',
        '--no-first-run',
        '--disable-default-apps',
        '--disable-popup-blocking',
        '--disable-translate',
        '--disable-extensions',
        '--disable-web-security',
        '--disable-features=site-per-process',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-background-networking',
        '--disable-sync',
        '--metrics-recording-only',
        '--no-report-upload',
        '--safebrowsing-disable-auto-update',
        '--enable-automation',
        '--password-store=basic',
        '--use-mock-keychain'
      ]
    };
  }

  getEnhancedHeadlessConfig() {
    return {
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--window-size=1920,1080',
        '--start-maximized',
        '--no-first-run',
        '--disable-default-apps',
        '--disable-popup-blocking',
        '--disable-translate',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-features=site-per-process',
        '--disable-hang-monitor',
        '--enable-automation',
        '--disable-prompt-on-repost'
      ]
    };
  }

  async cleanup() {
    if (this.xvfbProcess) {
      updateStatus({
        message: `Cleaning up Xvfb process (display :${this.displayNumber})`
      });

      try {
        this.xvfbProcess.kill('SIGTERM');

        // Wait for process to exit gracefully
        await new Promise((resolve) => {
          const timeout = setTimeout(() => {
            // Force kill if it doesn't exit gracefully
            this.xvfbProcess.kill('SIGKILL');
            resolve();
          }, 5000);

          this.xvfbProcess.on('exit', () => {
            clearTimeout(timeout);
            resolve();
          });
        });

        updateStatus({
          message: 'Xvfb process cleaned up successfully'
        });
      } catch (error) {
        updateStatus({
          message: `Error cleaning up Xvfb: ${error.message}`
        });
      }

      this.xvfbProcess = null;
      this.displayNumber = null;
    }

    // Clean up environment variable
    if (process.env.DISPLAY && this.displayNumber) {
      delete process.env.DISPLAY;
    }
  }
}

// Create a global instance
const virtualDisplayManager = new VirtualDisplayManager();

async function captureScreenshot(url, name, cropSettings = DEFAULT_CROP) {
  updateStatus({
    message: `Starting screenshot capture for ${name}`
  });

  // Set viewport dimensions
  const viewportWidth = 1920;
  const viewportHeight = 1080;

  // Setup virtual display
  let puppeteerConfig;
  try {
    puppeteerConfig = await virtualDisplayManager.setupVirtualDisplay();
  } catch (error) {
    updateStatus({
      message: `Virtual display setup failed: ${error.message}. Using fallback configuration.`
    });
    puppeteerConfig = virtualDisplayManager.getEnhancedHeadlessConfig();
  }

  const browser = await puppeteer.launch(puppeteerConfig);

  try {
    const page = await browser.newPage();

    // Configure page settings to prevent navigation issues
    page.setDefaultNavigationTimeout(90000); // 90 seconds
    page.setDefaultTimeout(90000);

    // Set user agent to avoid bot detection
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    // Set a fixed viewport size
    await page.setViewport({
      width: viewportWidth,
      height: viewportHeight,
      deviceScaleFactor: 1
    });

    // Add error handling for page events
    page.on('error', (error) => {
      updateStatus({
        message: `Page error for ${name}: ${error.message}`
      });
    });

    page.on('pageerror', (error) => {
      updateStatus({
        message: `Page script error for ${name}: ${error.message}`
      });
    });

    // Navigate with retry logic
    updateStatus({
      message: `Navigating to URL for ${name}...`
    });

    let navigationSuccess = false;
    let lastError = null;

    // Try navigation up to 3 times
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        updateStatus({
          message: `Navigation attempt ${attempt} for ${name}...`
        });

        await page.goto(url, {
          waitUntil: ['networkidle0', 'domcontentloaded'],
          timeout: 90000 // 90 seconds
        });

        navigationSuccess = true;
        break;
      } catch (error) {
        lastError = error;
        updateStatus({
          message: `Navigation attempt ${attempt} failed for ${name}: ${error.message}`
        });

        if (attempt < 3) {
          updateStatus({
            message: `Waiting 3 seconds before retry attempt ${attempt + 1}...`
          });
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    }

    if (!navigationSuccess) {
      throw new Error(`Failed to navigate after 3 attempts. Last error: ${lastError?.message}`);
    }

    updateStatus({
      message: `Page loaded for ${name}, waiting for content to render...`
    });

    // Wait for content to load and stabilize
    await new Promise(resolve => setTimeout(resolve, 8000)); // Reduced from 10000 to 8000

    // Additional check to ensure page is still responsive
    try {
      await page.evaluate(() => document.readyState);
      updateStatus({
        message: `Page is responsive for ${name}, proceeding with screenshot...`
      });
    } catch (error) {
      updateStatus({
        message: `Page responsiveness check failed for ${name}: ${error.message}`
      });
    }

    // Take a full screenshot without clipping
    updateStatus({
      message: `Taking screenshot for ${name}...`
    });

    let screenshotBuffer;
    try {
      screenshotBuffer = await page.screenshot({
        encoding: null, // Return as buffer
        fullPage: false, // Just capture the viewport
        type: 'png',
        captureBeyondViewport: false,
        clip: {
          x: 0,
          y: 0,
          width: viewportWidth,
          height: viewportHeight
        }
      });
    } catch (screenshotError) {
      updateStatus({
        message: `Screenshot capture failed for ${name}, trying fallback method: ${screenshotError.message}`
      });

      // Fallback: try without clip
      screenshotBuffer = await page.screenshot({
        encoding: null,
        fullPage: false,
        type: 'png'
      });
    }

    updateStatus({
      message: `Screenshot captured successfully for ${name}, now applying cropping...`
    });

    // Calculate cropped dimensions
    const extractWidth = viewportWidth - cropSettings.left - cropSettings.right;
    const extractHeight = viewportHeight - cropSettings.top - cropSettings.bottom;

    // Use Sharp to crop the image after capture
    const croppedImageBuffer = await sharp(screenshotBuffer)
      .extract({
        left: cropSettings.left,
        top: cropSettings.top,
        width: extractWidth,
        height: extractHeight
      })
      .toBuffer();

    // Convert the cropped buffer to base64
    const base64Image = croppedImageBuffer.toString('base64');

    updateStatus({
      message: `Cropping applied to ${name} using Sharp`
    });

    updateStatus({
      message: `Final image size: ${extractWidth}x${extractHeight}`
    });

    return base64Image;

  } catch (error) {
    const errorMsg = `Error capturing screenshot for ${name}: ${error.message}`;
    updateStatus({
      message: errorMsg
    });
    throw error;
  } finally {
    // Ensure browser is properly closed
    try {
      if (browser && browser.process() != null && !browser.process().killed) {
        await browser.close();
        updateStatus({
          message: `Browser closed successfully for ${name}`
        });
      }
    } catch (browserCloseError) {
      updateStatus({
        message: `Warning: Browser close failed for ${name}: ${browserCloseError.message}`
      });

      // Force kill browser process if normal close fails
      try {
        if (browser && browser.process() != null) {
          browser.process().kill('SIGKILL');
        }
      } catch (killError) {
        updateStatus({
          message: `Warning: Force kill browser failed for ${name}: ${killError.message}`
        });
      }
    }

    // Cleanup virtual display (only on Linux where Xvfb is used)
    if (virtualDisplayManager.platform === 'linux') {
      try {
        await virtualDisplayManager.cleanup();
      } catch (cleanupError) {
        updateStatus({
          message: `Warning: Virtual display cleanup failed: ${cleanupError.message}`
        });
      }
    }
  }
}

let scheduledJob = null;

// Status tracking for the screenshot process
let screenshotStatus = {
  inProgress: false,
  totalLinks: 0,
  processedLinks: 0,
  currentLink: null,
  currentStep: null,
  error: null,
  startTime: null,
  endTime: null,
  logs: []
};

// Helper function to update status and log
function updateStatus(update) {
  screenshotStatus = { ...screenshotStatus, ...update };

  // Add log entry if a message is provided
  if (update.message) {
    const logEntry = {
      time: new Date(),
      message: update.message
    };
    screenshotStatus.logs.push(logEntry);
    console.log(update.message);
  }

  return screenshotStatus;
}

async function scheduleScreenshots() {
  console.log('Setting up Screenshot Service...');
  if (scheduledJob) {
    console.log('Cancelling existing Screenshot Service');
    scheduledJob.cancel();
  }

  const settings = await DashboardSettings.findOne();

  if (!settings || !settings.screenshotSchedule.enabled) {
    console.log('Screenshot scheduling is disabled or no settings found');
    return;
  }

  // Parse start and end times
  const startHour = parseInt(settings.screenshotSchedule.startTime.split(':')[0]);
  const startMinute = parseInt(settings.screenshotSchedule.startTime.split(':')[1] || '0');
  const endHour = parseInt(settings.screenshotSchedule.endTime.split(':')[0]);
  const endMinute = parseInt(settings.screenshotSchedule.endTime.split(':')[1] || '0');

  // Create a more precise schedule using cron-style scheduling
  // Format: minute hour * * *
  // This will create a schedule that runs every X minutes between the start and end times
  const minutes = [];
  for (let min = 0; min < 60; min += settings.screenshotInterval) {
    minutes.push(min);
  }

  const cronExpression = `${minutes.join(',')} ${startHour}-${endHour} * * *`;
  console.log(`Screenshot Service will run every ${settings.screenshotInterval} minutes between ${startHour}:${startMinute.toString().padStart(2, '0')} and ${endHour}:${endMinute.toString().padStart(2, '0')}`);

  // We'll need to manually check for the end time minutes in the callback

  scheduledJob = schedule.scheduleJob(cronExpression, async () => {
    console.log('Screenshot schedule triggered - checking if within time window');

    // Check if we're still within the end time
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    // If we're at the end hour, check if we've passed the end minute
    if (currentHour === endHour && currentMinute > endMinute) {
      console.log(`Current time ${currentHour}:${currentMinute} is past end time ${endHour}:${endMinute}. Skipping screenshots.`);
      return;
    }

    try {
      console.log('Within scheduled time window. Taking screenshots now...');
      const currentSettings = await DashboardSettings.findOne();
      // Pass options to indicate this is a scheduled (not manual) update
      await takeScreenshots(currentSettings, { manual: false });
    } catch (error) {
      console.error('Error in scheduled screenshot job:', error);
    }
  });

  console.log('Screenshot Service successfully set up');
}

async function takeScreenshots(settings, options = {}) {
  // Reset and initialize status
  updateStatus({
    inProgress: true,
    totalLinks: 0,
    processedLinks: 0,
    currentLink: null,
    currentStep: 'initializing',
    error: null,
    startTime: new Date(),
    endTime: null,
    logs: [],
    message: 'Starting takeScreenshots process'
  });

  if (!settings || !settings.pbiLinks) {
    updateStatus({
      inProgress: false,
      error: 'No settings or pbiLinks found',
      endTime: new Date(),
      message: 'No settings or pbiLinks found'
    });
    return;
  }

  // Count enabled links
  const enabledLinks = settings.pbiLinks.filter(link => link.enabled);

  updateStatus({
    totalLinks: enabledLinks.length,
    message: `Processing ${enabledLinks.length} links`
  });

  updateStatus({
    message: `Current time: ${new Date().toLocaleString()}`
  });

  updateStatus({
    message: `Screenshot schedule settings: ${JSON.stringify({
      enabled: settings.screenshotSchedule.enabled,
      startTime: settings.screenshotSchedule.startTime,
      endTime: settings.screenshotSchedule.endTime,
      interval: settings.screenshotInterval
    })}`
  });

  // Check if this is a manual trigger
  const isManualTrigger = options.manual === true;
  updateStatus({
    currentStep: 'checking_schedule',
    message: isManualTrigger ? 'Manual trigger detected - bypassing schedule time window check.' : 'Checking schedule time window...'
  });

  if (!isManualTrigger) {
    // Only check time window for scheduled (non-manual) updates
    // Check if we're within the scheduled time window
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    const startHour = parseInt(settings.screenshotSchedule.startTime.split(':')[0]);
    const startMinute = parseInt(settings.screenshotSchedule.startTime.split(':')[1] || '0');
    const endHour = parseInt(settings.screenshotSchedule.endTime.split(':')[0]);
    const endMinute = parseInt(settings.screenshotSchedule.endTime.split(':')[1] || '0');

    // Check if current time is before start time
    if (currentHour < startHour || (currentHour === startHour && currentMinute < startMinute)) {
      const message = `Current time (${currentHour}:${currentMinute}) is before start time (${startHour}:${startMinute}). Skipping screenshots.`;
      updateStatus({
        inProgress: false,
        currentStep: 'outside_schedule',
        endTime: new Date(),
        message
      });
      return;
    }

    // Check if current time is after end time
    if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
      const message = `Current time (${currentHour}:${currentMinute}) is after end time (${endHour}:${endMinute}). Skipping screenshots.`;
      updateStatus({
        inProgress: false,
        currentStep: 'outside_schedule',
        endTime: new Date(),
        message
      });
      return;
    }

    updateStatus({
      message: `Current time (${currentHour}:${currentMinute}) is within scheduled window (${startHour}:${startMinute}-${endHour}:${endMinute}).`
    });
  }

  // Process each link sequentially to avoid opening too many browsers at once
  updateStatus({
    currentStep: 'processing_links',
    message: 'Starting to process dashboard links'
  });

  const updatedLinks = [];
  let processedCount = 0;

  for (const link of settings.pbiLinks) {
    if (link.enabled) {
      updateStatus({
        currentLink: link.name,
        message: `Processing link: ${link.name}`
      });

      try {
        // Add a small delay between screenshots to avoid resource contention
        if (updatedLinks.length > 0) {
          updateStatus({
            message: `Waiting 5 seconds before processing next dashboard...`
          });
          await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay between screenshots
        }

        // Update status for screenshot capture
        updateStatus({
          currentStep: 'capturing',
          message: `Starting screenshot capture for ${link.name}`
        });

        // Capture screenshot with Sharp post-processing
        const base64Image = await captureScreenshot(link.url, link.name);

        // Verify the image is valid and not too small
        updateStatus({
          currentStep: 'validating',
          message: `Validating screenshot for ${link.name}`
        });

        const imageBuffer = Buffer.from(base64Image, 'base64');
        if (imageBuffer.length < 10000) { // Less than 10KB is suspicious
          const warningMsg = `Warning: Screenshot for ${link.name} is suspiciously small (${imageBuffer.length} bytes). Using existing screenshot if available.`;
          updateStatus({
            message: warningMsg
          });

          if (link.screenshot) {
            updateStatus({
              message: `Keeping existing screenshot for ${link.name}`
            });
            updatedLinks.push(link.toObject());
            processedCount++;
            updateStatus({
              processedLinks: processedCount
            });
            continue;
          }
        }

        updateStatus({
          message: `Screenshot captured and validated for ${link.name} (${imageBuffer.length} bytes)`
        });

        updatedLinks.push({
          ...link.toObject(), // Convert mongoose document to plain object
          screenshot: base64Image
        });

        processedCount++;
        updateStatus({
          processedLinks: processedCount
        });

      } catch (error) {
        const errorMsg = `Error processing ${link.name}: ${error.message}`;
        updateStatus({
          message: errorMsg
        });

        // If there's an error, keep the existing screenshot if available
        if (link.screenshot) {
          updateStatus({
            message: `Keeping existing screenshot for ${link.name} due to error`
          });
          updatedLinks.push(link.toObject());
        } else {
          // If no existing screenshot, push without screenshot
          updateStatus({
            message: `No existing screenshot for ${link.name}`
          });
          const linkObj = link.toObject();
          delete linkObj.screenshot;
          updatedLinks.push(linkObj);
        }

        processedCount++;
        updateStatus({
          processedLinks: processedCount
        });
      }
    } else {
      updateStatus({
        message: `Skipping disabled link: ${link.name}`
      });
      updatedLinks.push(link.toObject());
    }
  }

  if (updatedLinks.length === 0) {
    updateStatus({
      inProgress: false,
      currentStep: 'completed',
      endTime: new Date(),
      message: 'No links to update. Skipping database update.'
    });
    return;
  }

  // Update database with new screenshots
  try {
    updateStatus({
      currentStep: 'saving',
      message: 'Saving screenshots to database...'
    });

    const result = await DashboardSettings.findOneAndUpdate(
      {},
      {
        $set: {
          pbiLinks: updatedLinks,
          updatedAt: new Date()
        }
      },
      { new: true }
    );

    updateStatus({
      inProgress: false,
      currentStep: 'completed',
      endTime: new Date(),
      message: 'Successfully updated screenshots in database'
    });

    return result;
  } catch (error) {
    const errorMsg = `Error updating screenshots in database: ${error.message}`;
    updateStatus({
      inProgress: false,
      currentStep: 'error',
      error: errorMsg,
      endTime: new Date(),
      message: errorMsg
    });
    throw error;
  }
}

// Function to get the current screenshot status
function getScreenshotStatus() {
  return { ...screenshotStatus };
}

module.exports = {
  captureScreenshot,
  scheduleScreenshots,
  takeScreenshots,
  getScreenshotStatus
};



