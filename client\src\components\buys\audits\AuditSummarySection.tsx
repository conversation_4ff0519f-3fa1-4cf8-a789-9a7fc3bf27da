import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { useFormContext } from 'react-hook-form';

interface AuditSummarySectionProps {
  control: any;
  disabled?: boolean;
  auditType: 'buy' | 'pawn' | 'price';
  overallScore?: number;
  failCount?: number;
  passCount?: number;
}

/**
 * Component for the Audit Summary section of the audit form
 * This section is shown for all audit types
 */
export function AuditSummarySection({
  control,
  disabled = false,
  auditType,
  overallScore = 0,
  failCount = 0,
  passCount = 0
}: AuditSummarySectionProps) {
  const { watch } = useFormContext();
  const flaggedForFollowup = watch('flaggedForFollowup');

  // Determine compliance level based on score or fail count
  const getComplianceLevel = () => {
    if (overallScore >= 90) return 'compliant';
    if (overallScore >= 70) return 'minor_non_compliant';
    return 'major_non_compliant';
  };

  const complianceLevel = getComplianceLevel();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Audit Summary</CardTitle>
        <CardDescription>Overall assessment and follow-up actions</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Compliance */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Overall Compliance</h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Score:</span>
              <Badge variant={overallScore >= 90 ? 'success' : overallScore >= 70 ? 'warning' : 'destructive'}>
                {overallScore}%
              </Badge>
              <span className="text-sm text-muted-foreground ml-2">Pass/Fail:</span>
              <Badge variant="outline">{passCount}/{failCount}</Badge>
            </div>
          </div>
          
          <FormField
            control={control}
            name="overallCompliance"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Assessment</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value || complianceLevel}
                    className="flex flex-col space-y-1"
                    disabled={disabled}
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="compliant" id="overallCompliant" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="overallCompliant">
                        Compliant
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="minor_non_compliant" id="overallMinorNonCompliant" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="overallMinorNonCompliant">
                        Minor Non-Compliant
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="major_non_compliant" id="overallMajorNonCompliant" />
                      </FormControl>
                      <FormLabel className="font-normal" htmlFor="overallMajorNonCompliant">
                        Major Non-Compliant
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormDescription>
                  The overall compliance is automatically calculated based on the assessments above,
                  but can be manually adjusted if needed.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Audit Notes */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Audit Notes</h3>
          
          <FormField
            control={control}
            name="auditNotes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any additional notes about the audit"
                    className="min-h-[100px]"
                    {...field}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Follow-up */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Follow-up</h3>

          <FormField
            control={control}
            name="flaggedForFollowup"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={disabled}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Flag for follow-up with employee
                  </FormLabel>
                  <FormDescription>
                    Check this if this audit requires follow-up with the employee
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* Conditional Follow-up Reason - only show when flagged for follow-up */}
          {flaggedForFollowup && (
            <FormField
              control={control}
              name="flagReason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Follow-up Reason <span className="text-destructive">*</span></FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter reason for follow-up (required when flagged)"
                      className="min-h-[100px]"
                      {...field}
                      disabled={disabled}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a detailed reason for why this audit requires follow-up
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
}
