import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { DollarSign, User, Package, FileText, Hash, Calendar } from 'lucide-react';

interface TransactionInfoHeaderProps {
  transactionId: string;
  stockcode?: string;
  buyer: string;
  cost: string | number;
  description: string;
  brand?: string;
  auditType: 'buy' | 'pawn' | 'price';
  transactionDate?: string;
  className?: string;
}

/**
 * Component that displays critical transaction information prominently
 * This replaces the need to hunt for information in tabs
 */
export function TransactionInfoHeader({
  transactionId,
  stockcode,
  buyer,
  cost,
  description,
  brand,
  auditType,
  transactionDate,
  className = ''
}: TransactionInfoHeaderProps) {
  const formatCost = (cost: string | number) => {
    const numericCost = typeof cost === 'string' ? parseFloat(cost) : cost;
    return isNaN(numericCost) ? cost : `$${numericCost.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getAuditTypeBadge = (type: string) => {
    switch (type) {
      case 'buy':
        return <Badge className="bg-blue-500 hover:bg-blue-600">Buy Audit</Badge>;
      case 'pawn':
        return <Badge className="bg-green-500 hover:bg-green-600">Pawn Loan Audit</Badge>;
      case 'price':
        return <Badge className="bg-purple-500 hover:bg-purple-600">Price Audit</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  return (
    <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b mb-6">
      <Card className={`border-2 border-primary/20 ${className}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-bold">Transaction Information</CardTitle>
            {getAuditTypeBadge(auditType)}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Transaction ID */}
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <Hash className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-muted-foreground">Transaction ID</p>
              <p className="text-lg font-semibold text-foreground truncate">{transactionId}</p>
            </div>
          </div>

          {/* Transaction Date */}
          {transactionDate && (
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Calendar className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-muted-foreground">Transaction Date</p>
                <p className="text-lg font-semibold text-foreground">{formatDate(transactionDate)}</p>
              </div>
            </div>
          )}

          {/* Stockcode */}
          {stockcode && (
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Package className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-muted-foreground">Stockcode</p>
                <p className="text-lg font-semibold text-foreground truncate">{stockcode}</p>
              </div>
            </div>
          )}

          {/* Buyer/Employee */}
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <User className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-muted-foreground">
                {auditType === 'pawn' ? 'Loan Officer' : 'Buyer'}
              </p>
              <p className="text-lg font-semibold text-foreground truncate">{buyer}</p>
            </div>
          </div>

          {/* Cost/Amount */}
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <DollarSign className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-muted-foreground">
                {auditType === 'pawn' ? 'Loan Amount' : 'Purchase Cost'}
              </p>
              <p className="text-lg font-semibold text-foreground">{formatCost(cost)}</p>
            </div>
          </div>

          {/* Brand */}
          {brand && (
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-muted-foreground">Brand</p>
                <p className="text-lg font-semibold text-foreground truncate">{brand}</p>
              </div>
            </div>
          )}

          {/* Description */}
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-muted-foreground">Item Description</p>
              <p className="text-lg font-semibold text-foreground truncate">{description}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
