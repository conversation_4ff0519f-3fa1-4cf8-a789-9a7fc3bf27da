import { CheckCircle2, Circle, CircleDot, FileText, ClipboardCheck, DollarSign, Shield, FileCheck } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface StepConfig {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  required: boolean;
}

interface FormProgressProps {
  auditType: 'buy' | 'pawn' | 'price';
  currentStep: number;
  onStepClick?: (step: number) => void;
  completedSteps?: number[];
}

/**
 * Enhanced component for displaying audit form progress with dynamic steps based on audit type
 */
export function FormProgress({ auditType, currentStep, onStepClick, completedSteps = [] }: FormProgressProps) {
  // Define step configurations for different audit types
  const getStepsConfig = (type: 'buy' | 'pawn' | 'price'): StepConfig[] => {
    const baseSteps: StepConfig[] = [
      {
        id: 'type',
        title: 'Select Type',
        description: 'Choose audit type',
        icon: FileText,
        required: true,
      },
      {
        id: 'details',
        title: 'Transaction Details',
        description: 'Enter transaction info & items',
        icon: ClipboardCheck,
        required: true,
      },
      {
        id: 'assessment',
        title: 'Item Assessment',
        description: 'Quality, condition & pricing',
        icon: DollarSign,
        required: true,
      },
    ];

    if (type === 'pawn') {
      baseSteps.push({
        id: 'pawn-questions',
        title: 'Pawn-Specific Questions',
        description: 'Responsible lending compliance',
        icon: Shield,
        required: true,
      });
    }

    baseSteps.push({
      id: 'summary',
      title: 'Authorization & Summary',
      description: 'Final review & submission',
      icon: FileCheck,
      required: true,
    });

    return baseSteps;
  };

  const steps = getStepsConfig(auditType);

  const getStepStatus = (stepIndex: number) => {
    if (completedSteps.includes(stepIndex + 1)) return 'completed';
    if (stepIndex + 1 === currentStep) return 'current';
    if (stepIndex + 1 < currentStep) return 'completed';
    return 'pending';
  };

  const getStepIcon = (step: StepConfig, status: string) => {
    const IconComponent = step.icon;

    if (status === 'completed') {
      return <CheckCircle2 className="h-5 w-5" />;
    } else if (status === 'current') {
      return <CircleDot className="h-5 w-5" />;
    } else {
      return <IconComponent className="h-5 w-5" />;
    }
  };

  const getStepStyles = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          circle: 'bg-green-600 border-green-600 text-white shadow-lg dark:bg-green-500 dark:border-green-500',
          title: 'text-green-700 font-semibold dark:text-green-400',
          description: 'text-green-600 dark:text-green-500',
          card: 'border-green-200 bg-green-50/50 dark:border-green-800/50 dark:bg-green-950/30',
        };
      case 'current':
        return {
          circle: 'bg-primary border-primary text-primary-foreground shadow-lg ring-4 ring-primary/20',
          title: 'text-primary font-semibold',
          description: 'text-primary/80',
          card: 'border-primary/30 bg-primary/5',
        };
      default:
        return {
          circle: 'bg-muted border-muted-foreground/30 text-muted-foreground',
          title: 'text-muted-foreground',
          description: 'text-muted-foreground/70',
          card: 'border-muted-foreground/20 bg-muted/20',
        };
    }
  };

  const isClickable = (stepIndex: number) => {
    return onStepClick && (stepIndex + 1 < currentStep || completedSteps.includes(stepIndex + 1));
  };

  return (
    <div className="mb-8">
      {/* Mobile Progress Bar */}
      <div className="block md:hidden mb-8">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium text-foreground">
            Step {currentStep} of {steps.length}
          </span>
          <Badge variant="outline" className="text-xs border-muted-foreground/30">
            {auditType.toUpperCase()} AUDIT
          </Badge>
        </div>
        <div className="w-full bg-muted/30 rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${(currentStep / steps.length) * 100}%` }}
          />
        </div>
        <div className="mt-3">
          <p className="text-sm font-medium text-foreground">{steps[currentStep - 1]?.title}</p>
          <p className="text-xs text-muted-foreground">{steps[currentStep - 1]?.description}</p>
        </div>
      </div>

      {/* Desktop Step Cards */}
      <div className="hidden md:block">
        {/* Dynamic grid based on number of steps */}
        <div className={cn(
          "grid gap-4 mb-6",
          steps.length === 4 ? "grid-cols-1 lg:grid-cols-4" : "grid-cols-1 lg:grid-cols-5"
        )}>
          {steps.map((step, index) => {
            const status = getStepStatus(index);
            const styles = getStepStyles(status);
            const clickable = isClickable(index);

            return (
              <Card
                key={step.id}
                className={cn(
                  'transition-all duration-200 hover:shadow-md',
                  styles.card,
                  clickable && 'cursor-pointer hover:scale-105',
                  !clickable && 'cursor-default'
                )}
                onClick={() => clickable && onStepClick?.(index + 1)}
              >
                <CardContent className="p-4">
                  <div className="flex flex-col items-center text-center space-y-3">
                    {/* Step Number & Icon */}
                    <div className="relative">
                      <div
                        className={cn(
                          'w-12 h-12 rounded-full border-2 flex items-center justify-center transition-all duration-200',
                          styles.circle
                        )}
                      >
                        {getStepIcon(step, status)}
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-background rounded-full flex items-center justify-center text-xs font-bold text-foreground border border-border">
                        {index + 1}
                      </div>
                    </div>

                    {/* Step Content */}
                    <div className="space-y-1">
                      <h3 className={cn('text-sm font-medium', styles.title)}>
                        {step.title}
                      </h3>
                      <p className={cn('text-xs leading-tight', styles.description)}>
                        {step.description}
                      </p>
                    </div>

                    {/* Status Indicator */}
                    {status === 'completed' && (
                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-400">
                        Complete
                      </Badge>
                    )}
                    {status === 'current' && (
                      <Badge variant="secondary" className="text-xs bg-primary/10 text-primary">
                        In Progress
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Connection Lines - Fixed positioning and width */}
        <div className="hidden lg:block relative mb-6">
          <div className="w-full h-0.5 bg-muted mx-auto" style={{ maxWidth: `${steps.length * 200}px` }}>
            <div
              className="h-full bg-primary transition-all duration-300 ease-in-out"
              style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
